import torch.nn as nn
import torch
import torch.nn as nn
import torch.nn.functional as F


class MergedModel(nn.Module):
    def __init__(self, **kargs):
        """
        双分支特征融合模型
        :param kargs: 包含以下参数
          - seq_branch_encoder: 序列分支的编码器
          - img_branch_encoder: 图像分支的编码器
          - seq_dim: 序列分支的输出维度
          - img_dim: 图像分支的输出维度
          - fusion_dim: 融合特征的维度 (默认:256)
          - num_classes: 输出类别数
        """
        super(MergedModel, self).__init__()
        self.seq_branch_encoder = kargs.get('seq_branch_encoder',None)
        self.img_branch_encoder = kargs.get('img_branch_encoder',None)
        if self.seq_branch_encoder is None:
            raise ValueError("请提供序列分支的编码器")
        if self.img_branch_encoder is None:
            raise ValueError("请提供图像分支的编码器")

        # 获取特征维度
        seq_dim = kargs.get('seq_dim', 128)
        img_dim = kargs.get('img_dim', 128)
        fusion_dim = kargs.get('fusion_dim', 256)

        # 特征融合模块
        self.seq_mlp = nn.Sequential(nn.Linear(seq_dim,seq_dim), nn.ReLU(), nn.Linear(seq_dim,fusion_dim//2))
        self.img_mlp = nn.Sequential(nn.Linear(img_dim,img_dim), nn.ReLU(), nn.Linear(img_dim,fusion_dim//2))
        # 分类器
        # 加载预训练权重（如果提供了路径）
        self._load_pretrained_weights(kargs)

    def _load_pretrained_weights(self, kargs):
        """
        加载预训练权重
        """
        # 加载序列分支预训练权重
        seq_pretrained_path = kargs.get('seq_pretrained_path', None)
        if seq_pretrained_path is not None:
            try:
                checkpoint = torch.load(seq_pretrained_path, map_location='cpu')
                # 如果保存的是整个模型状态，使用 'state_dict' 键
                if 'state_dict' in checkpoint:
                    state_dict = checkpoint['state_dict']
                    state_dict = {k.replace('module.', ''): v for k, v in state_dict.items()}
                    self.seq_branch_encoder.load_state_dict(state_dict)
                else:
                    # 如果直接保存了模型状态字典
                    self.seq_branch_encoder.load_state_dict(checkpoint)
                print(f"成功加载序列分支预训练权重: {seq_pretrained_path}")
            except Exception as e:
                print(f"加载序列分支预训练权重失败: {e}")

        # 加载图像分支预训练权重
        img_pretrained_path = kargs.get('img_pretrained_path', None)
        if img_pretrained_path is not None:
            try:
                checkpoint = torch.load(img_pretrained_path, map_location='cpu')
                # 如果保存的是整个模型状态，使用 'state_dict' 键
                if 'state_dict' in checkpoint:
                    state_dict = checkpoint['state_dict']
                    state_dict = {k.replace('module.', ''): v for k, v in state_dict.items()}
                    state_dict = {k.replace('backbone.', ''): v for k, v in state_dict.items()}
                    self.img_branch_encoder.load_state_dict(state_dict)
                else:
                    # 如果直接保存了模型状态字典
                    self.img_branch_encoder.load_state_dict(checkpoint)
                print(f"成功加载图像分支预训练权重: {img_pretrained_path}")
            except Exception as e:
                print(f"加载图像分支预训练权重失败: {e}")


    def forward(self, img_branch_input, seq_branch_input, attention_mask=None):
        """
        前向传播
        :param img_branch_input: 图像输入 [B, C, H, W]
        :param seq_branch_input: 序列输入 [B, seq_len]
        :param attention_mask: 注意力掩码 [B, seq_len] (可选)
        :return: 分类结果 [B, num_classes]
        """
        # 获取特征
        seq_features = self.seq_branch_encoder(seq_branch_input, attention_mask)  # [B, seq_dim]
        img_features = self.img_branch_encoder(img_branch_input)  # [B, img_dim]
        seq_out = self.seq_mlp(seq_features)
        img_out = self.img_mlp(img_features)


        return dict(seq_embedding=seq_out, img_embedding=img_out)



class ProtoSCL(nn.Module):
    """
    ProtoNet训练实现
    """
    def __init__(self, **kwargs):
        """
        ProtoNet模型初始化
        :param kwargs:
            - encoder: 特征提取器模型
            - temperature: 温度参数 (默认10.0)
            - pretrained_path: 预训练模型路径 (可选)
        """
        super(ProtoSCL, self).__init__()
        self.backbone = kwargs.get('backbone')
        self.n_way = kwargs.get('n_way', 10)  # 类别数
        self.k_shot = kwargs.get('k_shot', 10)
        self.k_query = kwargs.get('k_query', 20)
        self.n_views = kwargs.get('n_views', 2)  # 对比学习视图数
        self.simclr_batch = kwargs.get('simclr_batch', None)  # 对比学习批次大小
        self.simclr_temperature = 0.07
        self.protonet_temperature = 10.0
        self.device = kwargs.get('device', 'cpu')
        if self.backbone is None:
            raise ValueError("backbone must be provided in kwargs")
        # 真实的批次大小,我的对比学习任务数据直接从元学习任务中提取
        self.simclr_batch = min(self.simclr_batch, self.n_way * (self.k_shot + self.k_query))

    def get_embedding(self,DualBranchInput):
        """
        获取图像的嵌入特征
        输出Normalize之后的embedding
        """

        img_branch_input = DualBranchInput['image']
        seq_branch_input = DualBranchInput['sequence']
        outputs = self.backbone(img_branch_input=img_branch_input, seq_branch_input=seq_branch_input)
        seq_embedding = outputs['seq_embedding']
        img_embedding = outputs['img_embedding']
        img_embedding = F.normalize(img_embedding, dim=1)
        seq_embedding = F.normalize(seq_embedding, dim=1)
        return dict(seq_embedding=seq_embedding, img_embedding=img_embedding)
    def compute_prototypes(self, support_features):
        """
        计算类别原型

        Args:
            support_features: 支持集特征 [n_way * k_shot, dim]
            n_way: 类别数
            k_shot: 每类样本数

        Returns:
            prototypes: 类别原型 [n_way, dim]
        """
        n_way = self.n_way
        k_shot = self.k_shot
        # 重塑为 [n_way, k_shot, dim] 然后计算均值
        prototypes = support_features.reshape(n_way, k_shot, -1).mean(1)
        # prototypes的形状为 [n_way, dim]
        # 归一化原型
        return F.normalize(prototypes, dim=-1)

    def compute_logits(self, query_features, prototypes):
        """
        计算查询样本到原型的相似度logits

        Args:
            query_features: 查询集特征 [n_query, dim]
            prototypes: 类别原型 [n_way, dim]

        Returns:
            logits: 相似度logits [n_query, n_way]
        """
        # 余弦相似度 * 温度参数
        logits = torch.matmul(query_features, prototypes.t()) * self.protonet_temperature
        return logits

    def info_nce_loss(self, features):

        labels = torch.cat([torch.arange(self.simclr_batch) for i in range(self.n_views)], dim=0)
        labels = (labels.unsqueeze(0) == labels.unsqueeze(1)).float()
        labels = labels.to(self.device)

        features = F.normalize(features, dim=1)
        features = features.to(self.device)
        similarity_matrix = torch.matmul(features, features.T)
        # assert similarity_matrix.shape == (
        #     self.args.n_views * self.args.batch_size, self.args.n_views * self.args.batch_size)
        # assert similarity_matrix.shape == labels.shape

        # discard the main diagonal from both: labels and similarities matrix
        mask = torch.eye(labels.shape[0], dtype=torch.bool).to(self.device)
        labels = labels[~mask].view(labels.shape[0], -1)
        similarity_matrix = similarity_matrix[~mask].view(similarity_matrix.shape[0], -1)
        # assert similarity_matrix.shape == labels.shape

        # select and combine multiple positives
        positives = similarity_matrix[labels.bool()].view(labels.shape[0], -1)

        # select only the negatives the negatives
        negatives = similarity_matrix[~labels.bool()].view(similarity_matrix.shape[0], -1)

        logits = torch.cat([positives, negatives], dim=1)
        labels = torch.zeros(logits.shape[0], dtype=torch.long).to(self.device)

        logits = logits / self.simclr_temperature # 使用一个较小的温度参数
        return logits, labels

    def episode_forward(self, support_x, query_x):
        """
        处理一个元学习任务的前向传播
        :param support_x:
        :param query_x:
        :return:
        """
        # 提取特征
        support_features = self.get_embedding(support_x)  # [n_way * k_shot, dim]
        query_features = self.get_embedding(query_x)      # [n_way * q_query, dim]
        # 计算原型
        prototypes = self.compute_prototypes(support_features)
        # 计算logits
        logits = self.compute_logits(query_features, prototypes)

        return logits

    def forward(self, inputs):
        '''
        正常的前向传播
        :param x:
        :return:
        '''
        img_branch_input = inputs['image']
        img_branch_input = torch.cat(img_branch_input, dim=0)
        seq_branch_input = inputs['sequence']
        seq_branch_input = torch.cat(seq_branch_input, dim=0)
        outputs = self.backbone(img_branch_input=img_branch_input, seq_branch_input=seq_branch_input)
        return outputs