import os
import numpy as np
from PIL import Image
import pickle
from tqdm import tqdm


def convert_image_to_sequence(image_path):
    """将单张灰度图像转换为1D序列"""
    img = Image.open(image_path).convert('L')  # 确保是灰度模式
    return np.array(img).flatten()  # 展平为1D数组


def process_dataset(input_root, output_root):
    """
    处理整个数据集，保持目录结构
    :param input_root: 输入数据集根目录
    :param output_root: 输出数据集根目录
    """
    # 创建输出根目录
    os.makedirs(output_root, exist_ok=True)

    # 遍历输入目录中的所有文件
    image_files = []
    for root, dirs, files in os.walk(input_root):
        for file in files:
            if file.lower().endswith(('.png', '.jpg', '.jpeg')):
                image_files.append((root, file))

    # 使用tqdm展示整体进度
    for root, file in tqdm(image_files, desc="处理图片数据集", unit="image"):
        input_path = os.path.join(root, file)

        # 创建对应的输出目录结构
        rel_path = os.path.relpath(root, input_root)
        output_dir = os.path.join(output_root, rel_path)
        os.makedirs(output_dir, exist_ok=True)

        # 构建输出路径
        output_path = os.path.join(output_dir, os.path.splitext(file)[0] + '.pkl')

        # 转换并保存序列
        try:
            sequence = convert_image_to_sequence(input_path)
            with open(output_path, 'wb') as f:
                pickle.dump(sequence, f)
        except Exception as e:
            print(f"处理文件 {input_path} 时出错: {str(e)}")
            continue


if __name__ == "__main__":
    # 配置路径
    INPUT_DATASET = "./imgbranch"  # 替换为你的输入路径
    OUTPUT_DATASET = "./seqbranch"  # 替换为你的输出路径

    print(f"开始转换数据集: {INPUT_DATASET} → {OUTPUT_DATASET}")
    process_dataset(INPUT_DATASET, OUTPUT_DATASET)
    print("数据集转换完成！")