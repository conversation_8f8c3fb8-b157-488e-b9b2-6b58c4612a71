import torch
import torch.nn as nn
import torch.nn.functional as F



class ProtoSCL(nn.Module):
    """
    ProtoNet训练实现
    """
    def __init__(self, **kwargs):
        """
        ProtoNet模型初始化
        :param kwargs:
            - encoder: 特征提取器模型
            - temperature: 温度参数 (默认10.0)
            - pretrained_path: 预训练模型路径 (可选)
        """
        super(ProtoSCL, self).__init__()
        self.backbone = kwargs.get('backbone')
        self.n_way = kwargs.get('n_way', 10)  # 类别数
        self.k_shot = kwargs.get('k_shot', 10)
        self.k_query = kwargs.get('k_query', 20)
        self.n_views = kwargs.get('n_views', 2)  # 对比学习视图数
        self.simclr_batch = kwargs.get('simclr_batch', None)  # 对比学习批次大小
        self.simclr_temperature = 0.07
        self.protonet_temperature = 10.0
        self.device = kwargs.get('device', 'cpu')
        if self.backbone is None:
            raise ValueError("backbone must be provided in kwargs")
        # 真实的批次大小,我的对比学习任务数据直接从元学习任务中提取
        self.simclr_batch = min(self.simclr_batch, self.n_way * (self.k_shot + self.k_query))

    def get_embedding(self,DualBranchInput):
        """
        获取图像的嵌入特征
        """
        img_branch_input = DualBranchInput['image']
        seq_branch_input = DualBranchInput['sequence']
        outputs = self.backbone(img_branch_input=img_branch_input, seq_branch_input=seq_branch_input)
        out_embedding = outputs['out_embedding']
        return F.normalize(out_embedding, dim=-1)
    def compute_prototypes(self, support_features):
        """
        计算类别原型

        Args:
            support_features: 支持集特征 [n_way * k_shot, dim]
            n_way: 类别数
            k_shot: 每类样本数

        Returns:
            prototypes: 类别原型 [n_way, dim]
        """
        n_way = self.n_way
        k_shot = self.k_shot
        # 重塑为 [n_way, k_shot, dim] 然后计算均值
        prototypes = support_features.reshape(n_way, k_shot, -1).mean(1)
        # prototypes的形状为 [n_way, dim]
        # 归一化原型
        return F.normalize(prototypes, dim=-1)

    def compute_logits(self, query_features, prototypes):
        """
        计算查询样本到原型的相似度logits

        Args:
            query_features: 查询集特征 [n_query, dim]
            prototypes: 类别原型 [n_way, dim]

        Returns:
            logits: 相似度logits [n_query, n_way]
        """
        # 余弦相似度 * 温度参数
        logits = torch.matmul(query_features, prototypes.t()) * self.protonet_temperature
        return logits

    def info_nce_loss(self, features, class_labels=None, debug=False):
        """
        计算InfoNCE损失
        :param features: 特征向量 [batch_size * n_views, dim]
        :param class_labels: 类别标签 [batch_size] - 每个原始样本的真实类别标签
        :param debug: 是否打印调试信息
        :return: logits和标签
        """
        batch_size = features.size(0) // self.n_views

        if debug:
            print(f"\n=== InfoNCE Debug Info ===")
            print(f"Features shape: {features.shape}")
            print(f"Batch size: {batch_size}")
            print(f"N views: {self.n_views}")

        if class_labels is not None:
            # 使用真实类别标签构建正负样本对
            # 扩展类别标签到所有视图
            expanded_labels = torch.cat([class_labels for _ in range(self.n_views)], dim=0)
            expanded_labels = expanded_labels.to(self.device)

            # 构建样本索引（用于识别同一样本的不同视图）
            sample_indices = torch.cat([torch.arange(batch_size) for _ in range(self.n_views)], dim=0)
            sample_indices = sample_indices.to(self.device)


            # 正样本mask：同类别的样本 OR 同样本的不同视图
            class_mask = (expanded_labels.unsqueeze(0) == expanded_labels.unsqueeze(1)).float()
            view_mask = (sample_indices.unsqueeze(0) == sample_indices.unsqueeze(1)).float()
            positive_mask = torch.clamp(class_mask + view_mask, 0, 1)

            if debug:
                print(f"Class mask shape: {class_mask.shape}")
                print(f"View mask shape: {view_mask.shape}")
                print(f"Positive mask shape: {positive_mask.shape}")

                # 分析前几个样本的正样本情况
                for i in range(min(8, positive_mask.shape[0])):
                    pos_indices = torch.where(positive_mask[i] == 1)[0]
                    sample_idx = sample_indices[i].item()
                    class_label = expanded_labels[i].item()
                    view_id = i // batch_size
                    print(f"Sample {i}: orig_idx={sample_idx}, class={class_label}, view={view_id}, "
                          f"positives={len(pos_indices)}, pos_indices={pos_indices[:5].tolist()}...")

        else:
            # 原始逻辑：只有同样本的不同视图是正样本对
            sample_indices = torch.cat([torch.arange(batch_size) for _ in range(self.n_views)], dim=0)
            sample_indices = sample_indices.to(self.device)
            positive_mask = (sample_indices.unsqueeze(0) == sample_indices.unsqueeze(1)).float()

        features = F.normalize(features, dim=1)
        features = features.to(self.device)
        similarity_matrix = torch.matmul(features, features.T)

        if debug:
            print(f"Similarity matrix shape: {similarity_matrix.shape}")
            print(f"Similarity matrix diagonal (first 5): {torch.diag(similarity_matrix)[:5].tolist()}")

        # 移除对角线（自己与自己的相似度）
        n = positive_mask.shape[0]
        positive_mask_no_diag = torch.zeros(n, n-1).to(self.device)
        similarity_matrix_no_diag = torch.zeros(n, n-1).to(self.device)

        for i in range(n):
            # 对于第i行，移除第i列
            positive_mask_no_diag[i] = torch.cat([positive_mask[i, :i], positive_mask[i, i+1:]])
            similarity_matrix_no_diag[i] = torch.cat([similarity_matrix[i, :i], similarity_matrix[i, i+1:]])

        if debug:
            print(f"After removing diagonal:")
            print(f"Positive mask shape: {positive_mask_no_diag.shape}")
            print(f"Similarity matrix shape: {similarity_matrix_no_diag.shape}")

            # 检查前几个样本的正负样本数量
            for i in range(min(4, positive_mask_no_diag.shape[0])):
                pos_count = positive_mask_no_diag[i].sum().item()
                neg_count = (~positive_mask_no_diag[i].bool()).sum().item()
                print(f"Sample {i}: positives={pos_count}, negatives={neg_count}")

        # 选择正样本和负样本
        positives = similarity_matrix_no_diag[positive_mask_no_diag.bool()].view(positive_mask_no_diag.shape[0], -1)
        negatives = similarity_matrix_no_diag[~positive_mask_no_diag.bool()].view(similarity_matrix_no_diag.shape[0], -1)

        if debug:
            print(f"Positives shape: {positives.shape}")
            print(f"Negatives shape: {negatives.shape}")

        logits = torch.cat([positives, negatives], dim=1)
        labels = torch.zeros(logits.shape[0], dtype=torch.long).to(self.device)

        if debug:
            print(f"Final logits shape: {logits.shape}")
            print(f"Final labels shape: {labels.shape}")
            print(f"Labels (should all be 0): {labels[:5].tolist()}")
            print("=== End InfoNCE Debug ===\n")

        logits = logits / self.simclr_temperature
        return logits, labels

    def episode_forward(self, support_x, query_x):
        """
        处理一个元学习任务的前向传播
        :param support_x:
        :param query_x:
        :return:
        """
        # 提取特征
        support_features = self.get_embedding(support_x)  # [n_way * k_shot, dim]
        query_features = self.get_embedding(query_x)      # [n_way * q_query, dim]
        # 计算原型
        prototypes = self.compute_prototypes(support_features)
        # 计算logits
        logits = self.compute_logits(query_features, prototypes)

        return logits

    def forward(self, inputs):
        '''
        正常的前向传播
        :param x:
        :return:
        '''
        img_branch_input = inputs['image']
        img_branch_input = torch.cat(img_branch_input, dim=0)
        seq_branch_input = inputs['sequence']
        seq_branch_input = torch.cat(seq_branch_input, dim=0)
        outputs = self.backbone(img_branch_input=img_branch_input, seq_branch_input=seq_branch_input)
        return outputs


if __name__ == "__main__":
    print("=== Simple InfoNCE Test ===")

    # 简单测试参数
    batch_size = 6  # 小batch便于观察
    n_views = 2
    feature_dim = 4

    # 创建简单backbone
    class SimpleBackbone(nn.Module):
        def forward(self, **kwargs):
            return {}

    # 创建简单模型
    model = ProtoSCL(backbone=SimpleBackbone(), n_way=3, k_shot=2, k_query=2, n_views=n_views, simclr_batch=batch_size, device='cpu')

    # 创建测试数据
    features = torch.randn(batch_size * n_views, feature_dim)
    class_labels = torch.tensor([0, 0, 1, 1, 2, 2])  # 3个类别，每类2个样本

    print("=== 输入数据 ===")
    print(f"Features shape: {features.shape}")
    print(f"Features:\n{features}")
    print(f"Class labels: {class_labels}")

    # 扩展标签
    expanded_labels = torch.cat([class_labels for _ in range(n_views)], dim=0)
    sample_indices = torch.cat([torch.arange(batch_size) for _ in range(n_views)], dim=0)

    print(f"\nExpanded labels: {expanded_labels}")
    print(f"Sample indices: {sample_indices}")

    # 构建mask
    class_mask = (expanded_labels.unsqueeze(0) == expanded_labels.unsqueeze(1)).float()
    view_mask = (sample_indices.unsqueeze(0) == sample_indices.unsqueeze(1)).float()
    positive_mask = torch.clamp(class_mask + view_mask, 0, 1)

    print(f"\n=== Mask矩阵 ===")
    print(f"Class mask:\n{class_mask}")
    print(f"View mask:\n{view_mask}")
    print(f"Positive mask:\n{positive_mask}")

    # 使用与InfoNCE函数相同的移除对角线方法
    n = positive_mask.shape[0]
    positive_mask_no_diag = torch.zeros(n, n-1)
    for i in range(n):
        # 对于第i行，移除第i列（与InfoNCE函数保持一致）
        positive_mask_no_diag[i] = torch.cat([positive_mask[i, :i], positive_mask[i, i+1:]])

    print(f"\n=== 移除对角线后（正确版本）===")
    print(f"Positive mask (no diagonal):\n{positive_mask_no_diag}")

    # 验证每行的正样本数量
    print(f"\n=== 正样本数量验证 ===")
    for i in range(min(6, n)):
        original_positives = torch.where(positive_mask[i] == 1)[0].tolist()
        new_positives = torch.where(positive_mask_no_diag[i] == 1)[0].tolist()

        # 映射回原始索引
        mapped_positives = []
        for pos in new_positives:
            if pos >= i:
                mapped_positives.append(pos + 1)
            else:
                mapped_positives.append(pos)

        sample_idx = sample_indices[i].item()
        class_label = expanded_labels[i].item()
        view_id = 0 if i < batch_size else 1

        print(f"样本{i}(原始索引={sample_idx}, 类别={class_label}, 视图={view_id}):")
        print(f"  原始正样本: {original_positives}")
        print(f"  移除对角线后: {new_positives} -> 映射回原始: {mapped_positives}")
        print(f"  正样本数量: {len(mapped_positives)}")

        # 验证正样本的正确性
        for orig_idx in mapped_positives:
            pos_sample_idx = sample_indices[orig_idx].item()
            pos_class = expanded_labels[orig_idx].item()
            pos_view = 0 if orig_idx < batch_size else 1

            # 判断正样本类型
            if pos_sample_idx == sample_idx and pos_view != view_id:
                pos_type = "同样本其他视图"
            elif pos_class == class_label and pos_sample_idx != sample_idx:
                pos_type = "同类别其他样本"
            else:
                pos_type = "未知类型"

            print(f"    -> 索引{orig_idx}: 原始索引={pos_sample_idx}, 类别={pos_class}, 视图={pos_view} ({pos_type})")
        print()

    # 测试InfoNCE
    print(f"\n=== InfoNCE测试 ===")
    logits, targets = model.info_nce_loss(features, class_labels=class_labels, debug=True)
    print(f"Logits shape: {logits.shape}")
    print(f"Targets: {targets}")

    print("\n=== 测试完成 ===")