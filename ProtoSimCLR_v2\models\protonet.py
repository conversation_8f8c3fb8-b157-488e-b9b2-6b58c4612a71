import torch
import torch.nn as nn
import torch.nn.functional as F



class ProtoSCL(nn.Module):
    """
    ProtoNet训练实现
    """
    def __init__(self, **kwargs):
        """
        ProtoNet模型初始化
        :param kwargs:
            - encoder: 特征提取器模型
            - temperature: 温度参数 (默认10.0)
            - pretrained_path: 预训练模型路径 (可选)
        """
        super(ProtoSCL, self).__init__()
        self.backbone = kwargs.get('backbone')
        self.n_way = kwargs.get('n_way', 10)  # 类别数
        self.k_shot = kwargs.get('k_shot', 10)
        self.k_query = kwargs.get('k_query', 20)
        self.n_views = kwargs.get('n_views', 2)  # 对比学习视图数
        self.simclr_batch = kwargs.get('simclr_batch', None)  # 对比学习批次大小
        self.simclr_temperature = 0.07
        self.protonet_temperature = 10.0
        self.device = kwargs.get('device', 'cpu')
        if self.backbone is None:
            raise ValueError("backbone must be provided in kwargs")
        # 真实的批次大小,我的对比学习任务数据直接从元学习任务中提取
        self.simclr_batch = min(self.simclr_batch, self.n_way * (self.k_shot + self.k_query))

    def get_embedding(self,DualBranchInput):
        """
        获取图像的嵌入特征
        """
        img_branch_input = DualBranchInput['image']
        seq_branch_input = DualBranchInput['sequence']
        outputs = self.backbone(img_branch_input=img_branch_input, seq_branch_input=seq_branch_input)
        out_embedding = outputs['out_embedding']
        return F.normalize(out_embedding, dim=-1)
    def compute_prototypes(self, support_features):
        """
        计算类别原型

        Args:
            support_features: 支持集特征 [n_way * k_shot, dim]
            n_way: 类别数
            k_shot: 每类样本数

        Returns:
            prototypes: 类别原型 [n_way, dim]
        """
        n_way = self.n_way
        k_shot = self.k_shot
        # 重塑为 [n_way, k_shot, dim] 然后计算均值
        prototypes = support_features.reshape(n_way, k_shot, -1).mean(1)
        # prototypes的形状为 [n_way, dim]
        # 归一化原型
        return F.normalize(prototypes, dim=-1)

    def compute_logits(self, query_features, prototypes):
        """
        计算查询样本到原型的相似度logits

        Args:
            query_features: 查询集特征 [n_query, dim]
            prototypes: 类别原型 [n_way, dim]

        Returns:
            logits: 相似度logits [n_query, n_way]
        """
        # 余弦相似度 * 温度参数
        logits = torch.matmul(query_features, prototypes.t()) * self.protonet_temperature
        return logits

    def info_nce_loss(self, features, class_labels=None, debug=False):
        """
        计算InfoNCE损失
        :param features: 特征向量 [batch_size * n_views, dim]
        :param class_labels: 类别标签 [batch_size] - 每个原始样本的真实类别标签
        :param debug: 是否打印调试信息
        :return: logits和标签
        """
        batch_size = features.size(0) // self.n_views

        if debug:
            print(f"\n=== InfoNCE Debug Info ===")
            print(f"Features shape: {features.shape}")
            print(f"Batch size: {batch_size}")
            print(f"N views: {self.n_views}")

        if class_labels is not None:
            # 使用真实类别标签构建正负样本对
            # 扩展类别标签到所有视图
            expanded_labels = torch.cat([class_labels for _ in range(self.n_views)], dim=0)
            expanded_labels = expanded_labels.to(self.device)

            # 构建样本索引（用于识别同一样本的不同视图）
            sample_indices = torch.cat([torch.arange(batch_size) for _ in range(self.n_views)], dim=0)
            sample_indices = sample_indices.to(self.device)


            # 正样本mask：同类别的样本 OR 同样本的不同视图
            class_mask = (expanded_labels.unsqueeze(0) == expanded_labels.unsqueeze(1)).float()
            view_mask = (sample_indices.unsqueeze(0) == sample_indices.unsqueeze(1)).float()
            positive_mask = torch.clamp(class_mask + view_mask, 0, 1)

            if debug:
                print(f"Class mask shape: {class_mask.shape}")
                print(f"View mask shape: {view_mask.shape}")
                print(f"Positive mask shape: {positive_mask.shape}")

                # 分析前几个样本的正样本情况
                for i in range(min(8, positive_mask.shape[0])):
                    pos_indices = torch.where(positive_mask[i] == 1)[0]
                    sample_idx = sample_indices[i].item()
                    class_label = expanded_labels[i].item()
                    view_id = i // batch_size
                    print(f"Sample {i}: orig_idx={sample_idx}, class={class_label}, view={view_id}, "
                          f"positives={len(pos_indices)}, pos_indices={pos_indices[:5].tolist()}...")

        else:
            # 原始逻辑：只有同样本的不同视图是正样本对
            sample_indices = torch.cat([torch.arange(batch_size) for _ in range(self.n_views)], dim=0)
            sample_indices = sample_indices.to(self.device)
            positive_mask = (sample_indices.unsqueeze(0) == sample_indices.unsqueeze(1)).float()

        features = F.normalize(features, dim=1)
        features = features.to(self.device)
        similarity_matrix = torch.matmul(features, features.T)

        if debug:
            print(f"Similarity matrix shape: {similarity_matrix.shape}")
            print(f"Similarity matrix diagonal (first 5): {torch.diag(similarity_matrix)[:5].tolist()}")

        # 移除对角线（自己与自己的相似度）
        identity_mask = torch.eye(positive_mask.shape[0], dtype=torch.bool).to(self.device)
        positive_mask_no_diag = positive_mask[~identity_mask].view(positive_mask.shape[0], -1)
        similarity_matrix_no_diag = similarity_matrix[~identity_mask].view(similarity_matrix.shape[0], -1)

        if debug:
            print(f"After removing diagonal:")
            print(f"Positive mask shape: {positive_mask_no_diag.shape}")
            print(f"Similarity matrix shape: {similarity_matrix_no_diag.shape}")

            # 检查前几个样本的正负样本数量
            for i in range(min(4, positive_mask_no_diag.shape[0])):
                pos_count = positive_mask_no_diag[i].sum().item()
                neg_count = (~positive_mask_no_diag[i].bool()).sum().item()
                print(f"Sample {i}: positives={pos_count}, negatives={neg_count}")

        # 选择正样本和负样本
        positives = similarity_matrix_no_diag[positive_mask_no_diag.bool()].view(positive_mask_no_diag.shape[0], -1)
        negatives = similarity_matrix_no_diag[~positive_mask_no_diag.bool()].view(similarity_matrix_no_diag.shape[0], -1)

        if debug:
            print(f"Positives shape: {positives.shape}")
            print(f"Negatives shape: {negatives.shape}")

        logits = torch.cat([positives, negatives], dim=1)
        labels = torch.zeros(logits.shape[0], dtype=torch.long).to(self.device)

        if debug:
            print(f"Final logits shape: {logits.shape}")
            print(f"Final labels shape: {labels.shape}")
            print(f"Labels (should all be 0): {labels[:5].tolist()}")
            print("=== End InfoNCE Debug ===\n")

        logits = logits / self.simclr_temperature
        return logits, labels

    def episode_forward(self, support_x, query_x):
        """
        处理一个元学习任务的前向传播
        :param support_x:
        :param query_x:
        :return:
        """
        # 提取特征
        support_features = self.get_embedding(support_x)  # [n_way * k_shot, dim]
        query_features = self.get_embedding(query_x)      # [n_way * q_query, dim]
        # 计算原型
        prototypes = self.compute_prototypes(support_features)
        # 计算logits
        logits = self.compute_logits(query_features, prototypes)

        return logits

    def forward(self, inputs):
        '''
        正常的前向传播
        :param x:
        :return:
        '''
        img_branch_input = inputs['image']
        img_branch_input = torch.cat(img_branch_input, dim=0)
        seq_branch_input = inputs['sequence']
        seq_branch_input = torch.cat(seq_branch_input, dim=0)
        outputs = self.backbone(img_branch_input=img_branch_input, seq_branch_input=seq_branch_input)
        return outputs


if __name__ == "__main__":
    print("=== Testing InfoNCE Loss Implementation ===")

    # 创建简单的测试模型
    class SimpleBackbone(nn.Module):
        def __init__(self, input_dim=128, output_dim=64):
            super().__init__()
            self.fc = nn.Linear(input_dim, output_dim)

        def forward(self, img_branch_input, seq_branch_input):
            # 简化处理，只使用img_branch_input
            return {
                'seq_embedding': self.fc(img_branch_input),
                'img_embedding': self.fc(img_branch_input),
                'out_embedding': self.fc(img_branch_input)
            }

    # 测试参数
    batch_size = 32  # 对比学习batch中的样本数
    n_views = 2     # 每个样本的视图数
    feature_dim = 64
    n_classes = 8   # 类别数

    # 创建模型
    backbone = SimpleBackbone(input_dim=128, output_dim=feature_dim)
    model = ProtoSCL(
        backbone=backbone,
        n_way=5,
        k_shot=3,
        k_query=3,
        n_views=n_views,
        simclr_batch=batch_size,
        device='cpu'
    )

    print(f"Batch size: {batch_size}, Views: {n_views}, Feature dim: {feature_dim}")
    print(f"Total features: {batch_size * n_views}")

    # 测试1: 不使用类别标签的InfoNCE（原始SimCLR）
    print("\n--- Test 1: Original SimCLR (no class labels) ---")
    features = torch.randn(batch_size * n_views, feature_dim)
    logits, targets = model.info_nce_loss(features, class_labels=None)

    print(f"Features shape: {features.shape}")
    print(f"Logits shape: {logits.shape}")
    print(f"Targets shape: {targets.shape}")
    print(f"Targets (should all be 0): {targets[:10].tolist()}")

    # 验证正样本数量（每个样本应该有1个正样本：它的另一个视图）
    expected_positives_per_sample = 1
    actual_positives = logits.shape[1] - (batch_size * n_views - 1 - expected_positives_per_sample)
    print(f"Expected positives per sample: {expected_positives_per_sample}")
    print(f"Actual positives per sample: {actual_positives}")

    # 测试2: 使用类别标签的InfoNCE
    print("\n--- Test 2: Class-aware InfoNCE ---")
    # 创建类别标签：32个样本，8个类别，每个类别4个样本
    class_labels = torch.tensor([i // 4 for i in range(batch_size)])  # 8个类别，每类4个样本
    print(f"Class labels: {class_labels.tolist()}")
    print(f"Class distribution: {torch.bincount(class_labels).tolist()}")

    # 扩展到所有视图的标签
    expanded_labels = torch.cat([class_labels for _ in range(n_views)], dim=0)
    print(f"Expanded labels shape: {expanded_labels.shape}")
    print(f"First 16 expanded labels: {expanded_labels[:16].tolist()}")
    print(f"Last 16 expanded labels: {expanded_labels[-16:].tolist()}")

    logits_cls, targets_cls = model.info_nce_loss(features, class_labels=class_labels, debug=True)

    print(f"Logits shape: {logits_cls.shape}")
    print(f"Targets shape: {targets_cls.shape}")
    print(f"Targets (should all be 0): {targets_cls[:10].tolist()}")

    # 验证正样本数量（每个样本应该有：同类别样本数 + 同样本其他视图数 - 1）
    samples_per_class = 4  # 每个类别4个样本
    expected_positives_per_sample_cls = (samples_per_class - 1) + 1  # 同类别其他样本 + 同样本其他视图
    actual_positives_cls = logits_cls.shape[1] - (batch_size * n_views - 1 - expected_positives_per_sample_cls)
    print(f"Expected positives per sample (class-aware): {expected_positives_per_sample_cls}")
    print(f"Actual positives per sample (class-aware): {actual_positives_cls}")

    # 测试3: 验证正负样本对的正确性
    print("\n--- Test 3: Verify positive/negative pairs ---")

    def analyze_positive_mask(class_labels, n_views, batch_size):
        """分析正样本mask的构建是否正确"""
        expanded_labels = torch.cat([class_labels for _ in range(n_views)], dim=0)
        sample_indices = torch.cat([torch.arange(batch_size) for _ in range(n_views)], dim=0)

        # 构建mask
        class_mask = (expanded_labels.unsqueeze(0) == expanded_labels.unsqueeze(1)).float()
        view_mask = (sample_indices.unsqueeze(0) == sample_indices.unsqueeze(1)).float()
        positive_mask = torch.clamp(class_mask + view_mask, 0, 1)

        # 移除对角线
        identity_mask = torch.eye(positive_mask.shape[0], dtype=torch.bool)
        positive_mask_no_diag = positive_mask[~identity_mask].view(positive_mask.shape[0], -1)

        print(f"Expanded labels shape: {expanded_labels.shape}")
        print(f"Sample indices shape: {sample_indices.shape}")
        print(f"Positive mask shape: {positive_mask.shape}")
        print(f"Positive mask (no diagonal) shape: {positive_mask_no_diag.shape}")

        # 分析第一个样本的正样本
        first_sample_positives = positive_mask_no_diag[0].sum().item()
        print(f"First sample positive count: {first_sample_positives}")

        # 显示前几个样本的正样本分布
        for i in range(min(4, positive_mask_no_diag.shape[0])):
            pos_count = positive_mask_no_diag[i].sum().item()
            sample_idx = sample_indices[i].item()
            class_label = expanded_labels[i].item()
            view_id = i // batch_size
            print(f"Sample {i}: original_idx={sample_idx}, class={class_label}, view={view_id}, positives={pos_count}")

        return positive_mask_no_diag

    positive_mask = analyze_positive_mask(class_labels, n_views, batch_size)

    # 测试4: 损失计算
    print("\n--- Test 4: Loss computation ---")
    criterion = nn.CrossEntropyLoss()

    loss_original = criterion(logits, targets)
    loss_class_aware = criterion(logits_cls, targets_cls)

    print(f"Original SimCLR loss: {loss_original.item():.4f}")
    print(f"Class-aware InfoNCE loss: {loss_class_aware.item():.4f}")

    # 测试5: 边界情况
    print("\n--- Test 5: Edge cases ---")

    # 所有样本都是同一类别
    same_class_labels = torch.zeros(batch_size, dtype=torch.long)
    logits_same, targets_same = model.info_nce_loss(features, class_labels=same_class_labels)
    print(f"All same class - Logits shape: {logits_same.shape}")

    # 所有样本都是不同类别
    diff_class_labels = torch.arange(batch_size)
    logits_diff, targets_diff = model.info_nce_loss(features, class_labels=diff_class_labels)
    print(f"All different classes - Logits shape: {logits_diff.shape}")

    print("\n=== InfoNCE Loss Test Completed ===")

    # 原始测试代码（保留）
    print("\n=== Original ProtoNet Test ===")
    try:
        from resnet_simclr import ResNetSimCLR
        n_way = 5
        k_shot = 3
        q_query = 3
        model_original = ProtoSCL(backbone=ResNetSimCLR(base_model='resnet18', out_dim=128),
                        n_way=n_way, k_shot=k_shot, temperature=10.0)
        support_x = torch.randn(15, 3, 224, 224)  # 5 classes, 3 shots each
        query_x = torch.randn(15, 3, 224, 224)    # 5 classes, 3 queries each

        logits = model_original(support_x, query_x)
        print(f"ProtoNet logits shape: {logits.shape}")  # Should be [15, 5] for the example above
    except ImportError:
        print("ResNetSimCLR not available, skipping original test")